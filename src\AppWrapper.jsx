import { useState, useEffect } from "react";
import App from "./App";
import { TeethProvider } from "./context/TeethContext";

const AppWrapper = () => {
  const [initialTeethData, setInitialTeethData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  const url = new URL(window.location.href);
  const viewParam = url.searchParams.get("view");
  const isSingleTreatmentView = viewParam === "single_treatment";

  useEffect(() => {
    const loadInitialData = async () => {
      try {
        if (isSingleTreatmentView) {
          // Single treatment view data
          const singleToothData = {
            patientType: "ADULT", // Can be "ADULT" or "CHILDREN"
            patientId: "ST12345", // Patient ID for single treatment view
            1: {
              position: "UL8",
              position_number: 1,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "Single tooth for treatment view",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [
                {
                  Id: "111",
                  ctid: "222",
                  completed: false,
                  name: "CoreBuildUp",
                  full_tooth_treatment: true,
                  patient_treatment: false,
                  remove_tooth_when_completed: false,
                  remove_treatment_when_completed: false,
                  bridge_treatment: false,
                  missing_tooth_indicator: false,
                  mixed_dentition: false,
                  created_at: "01-01-2025",
                  completed_at: null,
                },
                {
                  Id: "112",
                  ctid: "223",
                  completed: false,
                  name: "Apictomy",
                  full_tooth_treatment: true,
                  patient_treatment: false,
                  remove_tooth_when_completed: false,
                  remove_treatment_when_completed: false,
                  bridge_treatment: false,
                  missing_tooth_indicator: false,
                  mixed_dentition: false,
                  created_at: "02-06-2025",
                  completed_at: null,
                },
                {
                  Id: "112",
                  ctid: "223",
                  completed: false,
                  name: "Abcess",
                  full_tooth_treatment: true,
                  patient_treatment: false,
                  remove_tooth_when_completed: false,
                  remove_treatment_when_completed: false,
                  bridge_treatment: false,
                  missing_tooth_indicator: false,
                  mixed_dentition: false,
                  created_at: "02-02-2025",
                  completed_at: null,
                },
                {
                  Id: "112",
                  ctid: "223",
                  completed: false,
                  name: "RetainedRoot",
                  full_tooth_treatment: true,
                  patient_treatment: false,
                  remove_tooth_when_completed: false,
                  remove_treatment_when_completed: false,
                  bridge_treatment: false,
                  missing_tooth_indicator: false,
                  mixed_dentition: false,
                  created_at: "02-05-2025",
                  completed_at: null,
                },
                {
                  Id: "112",
                  ctid: "223",
                  completed: false,
                  name: "Post&Core",
                  full_tooth_treatment: true,
                  patient_treatment: false,
                  remove_tooth_when_completed: false,
                  remove_treatment_when_completed: false,
                  bridge_treatment: false,
                  missing_tooth_indicator: false,
                  mixed_dentition: false,
                  created_at: "02-02-2022",
                  completed_at: null,
                },
              ],
            },
          };

          setInitialTeethData(singleToothData);
        } else {
          // Define all teeth data as a big object
          const sampleData = {
            patientType: "ADULT", // Can be "ADULT" or "CHILDREN"
            patientId: "PT12345", // Patient ID for regular views
            1: {
              position: "UL8",
              position_number: 1,
              status: "status",
              lastTreatment: "02-04-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [
                {
                  Id: "111",
                  ctid: "222",
                  completed: false,
                  name: "CoreBuildUp",
                  full_tooth_treatment: true,
                  patient_treatment: false,
                  remove_tooth_when_completed: false,
                  remove_treatment_when_completed: false,
                  bridge_treatment: false,
                  missing_tooth_indicator: false,
                  mixed_dentition: false,
                  created_at: "01-01-2025",
                  completed_at: null,
                },
                {
                  Id: "112",
                  ctid: "223",
                  completed: false,
                  name: "Apictomy",
                  full_tooth_treatment: true,
                  patient_treatment: false,
                  remove_tooth_when_completed: false,
                  remove_treatment_when_completed: false,
                  bridge_treatment: false,
                  missing_tooth_indicator: false,
                  mixed_dentition: false,
                  created_at: "02-06-2025",
                  completed_at: null,
                },
                {
                  Id: "112",
                  ctid: "223",
                  completed: false,
                  name: "Abcess",
                  full_tooth_treatment: true,
                  patient_treatment: false,
                  remove_tooth_when_completed: false,
                  remove_treatment_when_completed: false,
                  bridge_treatment: false,
                  missing_tooth_indicator: false,
                  mixed_dentition: false,
                  created_at: "02-02-2025",
                  completed_at: null,
                },
                {
                  Id: "112",
                  ctid: "223",
                  completed: false,
                  name: "RetainedRoot",
                  full_tooth_treatment: true,
                  patient_treatment: false,
                  remove_tooth_when_completed: false,
                  remove_treatment_when_completed: false,
                  bridge_treatment: false,
                  missing_tooth_indicator: false,
                  mixed_dentition: false,
                  created_at: "02-05-2025",
                  completed_at: null,
                },
                {
                  Id: "112",
                  ctid: "223",
                  completed: false,
                  name: "Post&Core",
                  full_tooth_treatment: true,
                  patient_treatment: false,
                  remove_tooth_when_completed: false,
                  remove_treatment_when_completed: false,
                  bridge_treatment: false,
                  missing_tooth_indicator: false,
                  mixed_dentition: false,
                  created_at: "02-02-2022",
                  completed_at: null,
                },
              ],
            },
            2: {
              position: "UL8",
              status: "filled",
              lastTreatment: "2024-02-20",
              notes: "Large filling placed",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [
                {
                  id: "111",
                  ctid: "222",
                  name: "Filling",
                  full_tooth_treatment: false,
                  created_at: "2024-02-20",
                  surfaces: {
                    // DistalOcclusal: { decaySeverity: 0.5, fillingSize: 0.1 },
                    Distal: { decaySeverity: 0, fillingSize: 0.5 },
                    // DistalBuccal: { decaySeverity: 0.5, fillingSize: 0.5 },
                    // DistalPalatal: { decaySeverity: 0.5, fillingSize: 0.5 },
                    // Mesial: { decaySeverity: 0.5, fillingSize: 0.5 },
                    // MesialBuccal: { decaySeverity: 0.5, fillingSize: 0.5 },
                    // MesialOcclusal: { decaySeverity: 0.5, fillingSize: 0.5 },
                    // MesialPalatal: { decaySeverity: 0.5, fillingSize: 0.5 },
                  },
                },
              ],
            },
            3: {
              position: "UL6",
              position_number: 3,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            4: {
              position: "UL5",
              position_number: 4,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            5: {
              position: "UL4",
              position_number: 5,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            6: {
              position: "UL3",
              position_number: 6,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            7: {
              position: "UL2",
              position_number: 7,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            8: {
              position: "UL1",
              position_number: 8,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            9: {
              position: "UR1",
              position_number: 9,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [
                {
                  Id: "111",
                  ctid: "222",
                  completed: false,
                  name: "Clasp",
                  full_tooth_treatment: true,
                  patient_treatment: false,
                  remove_tooth_when_completed: false,
                  remove_treatment_when_completed: false,
                  bridge_treatment: false,
                  missing_tooth_indicator: false,
                  mixed_dentition: false,
                  created_at: "01-01-2025",
                  completed_at: null,
                },
              ],
            },
            10: {
              position: "UR2",
              position_number: 10,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            11: {
              position: "UR3",
              position_number: 11,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            12: {
              position: "UR4",
              position_number: 12,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            13: {
              position: "UR5",
              position_number: 13,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            14: {
              position: "UR6",
              position_number: 14,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            15: {
              position: "UR7",
              position_number: 15,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            16: {
              position: "UR8",
              position_number: 16,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            17: {
              position: "LR8",
              position_number: 17,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            18: {
              position: "LR7",
              position_number: 18,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            19: {
              position: "LR6",
              position_number: 19,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            20: {
              position: "LR5",
              position_number: 20,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            21: {
              position: "LR4",
              position_number: 21,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            22: {
              position: "LR3",
              position_number: 22,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            23: {
              position: "LR2",
              position_number: 23,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            24: {
              position: "LR1",
              position_number: 24,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            25: {
              position: "LL1",
              position_number: 25,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            26: {
              position: "LL2",
              position_number: 26,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            27: {
              position: "LL3",
              position_number: 27,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            28: {
              position: "LL4",
              position_number: 28,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            29: {
              position: "LL5",
              position_number: 29,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            30: {
              position: "LL6",
              position_number: 30,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            31: {
              position: "LL7",
              position_number: 31,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
            32: {
              position: "LL8",
              position_number: 32,
              status: "status",
              lastTreatment: "01-01-2025",
              notes: "notes",
              marked_as_missing: false,
              marked_as_watched: false,
              treatments: [],
            },
          };

          setInitialTeethData(sampleData);
        }
      } catch (error) {
        console.error("Error loading initial teeth data:", error);
        setInitialTeethData({});
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, [isSingleTreatmentView]);

  if (isLoading) {
    return <div className="loading">Loading patient data...</div>;
  }

  return (
    <TeethProvider initialTeeth={initialTeethData}>
      <App />
    </TeethProvider>
  );
};

export default AppWrapper;
