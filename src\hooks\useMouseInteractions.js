import { useRef, useState, useEffect, useCallback } from "react";
import * as THREE from "three";
import { createHighlightMaterial } from "../utils/modelUtils";

export const useMouseInteractions = ({
  camera,
  gl,
  teethRef,
  setHoveredTooth,
  setSelectedTooth,
  openRightClickModal,
}) => {
  const raycasterRef = useRef(new THREE.Raycaster());
  const mouseRef = useRef(new THREE.Vector2());
  const prevHoveredRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const lastEventTimeRef = useRef(0);
  const mountedRef = useRef(true);
  const highlightedMeshesRef = useRef(new Set());

  // Get the current view type from the first tooth (if any)
  const getViewType = useCallback(() => {
    if (!teethRef?.current?.size) return "unknown";
    const firstTooth = teethRef.current.values().next().value;
    return firstTooth?.userData?.viewType || "unknown";
  }, [teethRef]);

  // Reset all highlighted materials when unmounting
  const resetAllHighlightedMeshes = useCallback(() => {
    if (highlightedMeshesRef.current.size > 0) {
      highlightedMeshesRef.current.forEach((mesh) => {
        if (mesh.userData.originalMaterial) {
          // Restore the original material
          mesh.material = mesh.userData.originalMaterial.clone();
          // Clean up the reference when unmounting
          delete mesh.userData.originalMaterial;
        }
      });

      highlightedMeshesRef.current.clear();
    }
  }, []);

  // Get all interactive objects from the teeth
  const getInteractiveObjects = useCallback(() => {
    if (!teethRef || !teethRef.current) {
      return [];
    }

    const objects = [];

    // Skip logging on every mouse move
    const shouldLog = Date.now() - lastEventTimeRef.current > 1000;
    if (shouldLog) {
      lastEventTimeRef.current = Date.now();
    }

    teethRef.current.forEach((tooth) => {
      if (tooth) {
        tooth.traverse((child) => {
          if (child.isMesh && child.userData.isInteractive) {
            objects.push(child);
          }
        });
      }
    });

    return objects;
  }, [teethRef]);

  // Check if a tooth should skip material changes
  const shouldSkipMaterialChange = useCallback(
    (toothNumber) => {
      const tooth = teethRef.current.get(toothNumber);
      if (!tooth) return false;

      // Check if the tooth name contains "Decay" or "filling"
      const toothName = tooth.name || "";
      return toothName.includes("Decay") || toothName.includes("Filling");
    },
    [teethRef],
  );

  // Handle hover state changes
  const handleHover = useCallback(
    (toothNumber) => {
      if (!mountedRef.current) return;
      if (prevHoveredRef.current === toothNumber) return;

      const viewType = getViewType();

      // Reset previous hover
      if (prevHoveredRef.current) {
        const prevTooth = teethRef.current.get(prevHoveredRef.current);
        const skipPrevMaterialChange = shouldSkipMaterialChange(
          prevHoveredRef.current,
        );

        if (prevTooth) {
          prevTooth.traverse((child) => {
            // Only restore material if it's not a Decay or filling tooth
            if (
              child.isMesh &&
              child.userData.originalMaterial &&
              highlightedMeshesRef.current.has(child) &&
              !skipPrevMaterialChange
            ) {
              try {
                // Build debug props objects, filtering out undefined values
                const originalProps = {};
                const currentProps = {};
                const props = [
                  "transparent",
                  "opacity",
                  "renderOrder",
                  "depthWrite",
                  "depthTest",
                  "blending",
                ];

                props.forEach((prop) => {
                  if (child.userData.originalMaterial[prop] !== undefined) {
                    originalProps[prop] = child.userData.originalMaterial[prop];
                  }
                  if (child.material[prop] !== undefined) {
                    currentProps[prop] = child.material[prop];
                  }
                });

                console.log("[DEBUG] Restoring material for mesh:", {
                  meshName: child.name,
                  toothNumber: prevHoveredRef.current,
                  originalMaterialProps: originalProps,
                  currentMaterialProps: currentProps,
                });

                // Create a new instance of the material to avoid sharing
                const restoredMaterial =
                  child.userData.originalMaterial.clone();

                // Ensure all transparency-related properties are properly restored
                if (child.userData.originalMaterial.transparent !== undefined) {
                  restoredMaterial.transparent =
                    child.userData.originalMaterial.transparent;
                }
                if (child.userData.originalMaterial.opacity !== undefined) {
                  restoredMaterial.opacity =
                    child.userData.originalMaterial.opacity;
                }
                if (child.userData.originalMaterial.depthWrite !== undefined) {
                  restoredMaterial.depthWrite =
                    child.userData.originalMaterial.depthWrite;
                }
                if (child.userData.originalMaterial.depthTest !== undefined) {
                  restoredMaterial.depthTest =
                    child.userData.originalMaterial.depthTest;
                }
                if (child.userData.originalMaterial.blending !== undefined) {
                  restoredMaterial.blending =
                    child.userData.originalMaterial.blending;
                }
                if (child.userData.originalMaterial.alphaTest !== undefined) {
                  restoredMaterial.alphaTest =
                    child.userData.originalMaterial.alphaTest;
                }

                // Preserve additional material properties that might be lost
                if (child.userData.originalMaterial.renderOrder !== undefined) {
                  restoredMaterial.renderOrder =
                    child.userData.originalMaterial.renderOrder;
                }
                if (
                  child.userData.originalMaterial.polygonOffset !== undefined
                ) {
                  restoredMaterial.polygonOffset =
                    child.userData.originalMaterial.polygonOffset;
                }
                if (
                  child.userData.originalMaterial.polygonOffsetFactor !==
                  undefined
                ) {
                  restoredMaterial.polygonOffsetFactor =
                    child.userData.originalMaterial.polygonOffsetFactor;
                }
                if (
                  child.userData.originalMaterial.polygonOffsetUnits !==
                  undefined
                ) {
                  restoredMaterial.polygonOffsetUnits =
                    child.userData.originalMaterial.polygonOffsetUnits;
                }
                if (child.userData.originalMaterial.side !== undefined) {
                  restoredMaterial.side = child.userData.originalMaterial.side;
                }
                if (child.userData.originalMaterial.color !== undefined) {
                  restoredMaterial.color.copy(
                    child.userData.originalMaterial.color,
                  );
                }
                if (child.userData.originalMaterial.emissive !== undefined) {
                  restoredMaterial.emissive.copy(
                    child.userData.originalMaterial.emissive,
                  );
                }
                if (child.userData.originalMaterial.metalness !== undefined) {
                  restoredMaterial.metalness =
                    child.userData.originalMaterial.metalness;
                }
                if (child.userData.originalMaterial.roughness !== undefined) {
                  restoredMaterial.roughness =
                    child.userData.originalMaterial.roughness;
                }

                // Apply the restored material
                child.material = restoredMaterial;
                highlightedMeshesRef.current.delete(child);

                // Build restored props for debugging
                const restoredProps = {};
                props.forEach((prop) => {
                  if (child.material[prop] !== undefined) {
                    restoredProps[prop] = child.material[prop];
                  }
                });

                console.log("[DEBUG] After restoration:", {
                  meshName: child.name,
                  restoredMaterialProps: restoredProps,
                });

                // IMPORTANT: Do NOT delete the originalMaterial reference
                // We need to keep it for future hover/unhover cycles
                // Only clear it when the component unmounts or the tooth is removed
              } catch (e) {
                console.error(
                  `[${viewType}] MouseInteractions: Error restoring original material:`,
                  e,
                );
              }
            }
          });
        }
      }

      // Set new hover
      if (toothNumber) {
        const tooth = teethRef.current.get(toothNumber);
        const skipMaterialChange = shouldSkipMaterialChange(toothNumber);

        if (tooth) {
          tooth.traverse((child) => {
            if (child.isMesh) {
              // Skip material change for Decay or filling teeth but still track for hover state
              if (!skipMaterialChange) {
                // Store the original material ONLY if not already stored
                // This ensures we always keep the true original material
                if (!child.userData.originalMaterial) {
                  // Build debug props object
                  const materialProps = {};
                  const props = [
                    "type",
                    "transparent",
                    "opacity",
                    "renderOrder",
                    "depthWrite",
                    "depthTest",
                    "blending",
                    "polygonOffset",
                    "polygonOffsetFactor",
                    "polygonOffsetUnits",
                    "alphaTest",
                  ];

                  props.forEach((prop) => {
                    if (child.material[prop] !== undefined) {
                      materialProps[prop] = child.material[prop];
                    }
                  });

                  console.log("[DEBUG] Storing original material for mesh:", {
                    meshName: child.name,
                    toothNumber: toothNumber,
                    materialProps: materialProps,
                  });

                  // Store a clone of the original material
                  child.userData.originalMaterial = child.material.clone();
                } else {
                  console.log(
                    "[DEBUG] Original material already exists for mesh:",
                    {
                      meshName: child.name,
                      toothNumber: toothNumber,
                      hasOriginal: !!child.userData.originalMaterial,
                    },
                  );
                }

                // Set highlight material - create a new one each time, preserving transparency
                child.material = createHighlightMaterial(
                  child.userData.originalMaterial,
                );

                // Add to tracked set
                highlightedMeshesRef.current.add(child);
              }
            }
          });
        }
      }

      prevHoveredRef.current = toothNumber;
      // Always update hovered tooth state regardless of material change
      setHoveredTooth(toothNumber);
    },
    [getViewType, setHoveredTooth, shouldSkipMaterialChange, teethRef],
  );

  // Handle mouse move events
  const handleMouseMove = useCallback(
    (event) => {
      if (!mountedRef.current || !isInitialized) return;

      const rect = gl.domElement.getBoundingClientRect();
      mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      raycasterRef.current.setFromCamera(mouseRef.current, camera);

      const interactiveObjects = getInteractiveObjects();

      if (interactiveObjects.length === 0) return;

      const intersects = raycasterRef.current.intersectObjects(
        interactiveObjects,
        true,
      );

      if (intersects.length > 0) {
        const toothNumber = parseInt(intersects[0].object.userData.number);
        if (toothNumber) {
          handleHover(toothNumber);
        }
      } else {
        handleHover(null);
      }
    },
    [camera, getInteractiveObjects, gl, handleHover, isInitialized],
  );

  // Handle click events
  const handleClick = useCallback(
    (event) => {
      if (!mountedRef.current || !isInitialized) return;

      const rect = gl.domElement.getBoundingClientRect();
      mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      raycasterRef.current.setFromCamera(mouseRef.current, camera);

      const interactiveObjects = getInteractiveObjects();
      if (interactiveObjects.length === 0) return;

      const intersects = raycasterRef.current.intersectObjects(
        interactiveObjects,
        true,
      );

      if (intersects.length > 0) {
        const toothNumber = parseInt(intersects[0].object.userData.number);
        if (event.button === 0) {
          // Left click
          // We still want to handle selection for all teeth, even Decay/filling ones
          setSelectedTooth((prev) =>
            prev === toothNumber ? null : toothNumber,
          );
          // console.log('Left Click on tooth:', toothNumber)
        } else if (event.button === 2) {
          // Right click
          event.preventDefault(); // Prevent context menu
          const modalWidth = 300;
          const modalHeight = 150;
          const padding = 35;

          const x = Math.min(
            window.innerWidth - modalWidth - padding,
            event.clientX,
          );
          const y = Math.min(
            window.innerHeight - modalHeight - padding,
            event.clientY,
          );
          openRightClickModal({ tooth: toothNumber, cursor: { x: x, y: y } });
          // console.log('Right click on tooth:', toothNumber);
        }
      } else {
        setSelectedTooth(null);
      }
    },
    [getInteractiveObjects, gl, isInitialized, setSelectedTooth, camera],
  );

  // Initialize when teeth are available
  useEffect(() => {
    if (teethRef?.current?.size > 0 && !isInitialized) {
      setIsInitialized(true);
    }
  }, [teethRef, isInitialized]);

  // Watch for changes in the teeth ref size with interval polling
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (teethRef?.current?.size > 0 && !isInitialized) {
        setIsInitialized(true);
        clearInterval(intervalId);
      }
    }, 500);

    return () => {
      clearInterval(intervalId);
    };
  }, [teethRef, isInitialized]);

  // Attach event listeners
  useEffect(() => {
    if (!isInitialized || !gl) return;

    const viewType = getViewType();

    // Validate that we have teeth refs before setting up event handlers
    if (!teethRef || !teethRef.current) {
      console.error(`[${viewType}] MouseInteractions: Missing teeth ref`);
      return;
    }

    const canvas = gl.domElement;
    canvas.style.touchAction = "none";

    // Use throttled versions of event handlers for better performance
    let lastMoveTime = 0;
    const throttleTime = 50; // Use original throttle time for better responsiveness

    const throttledMouseMove = (event) => {
      const now = Date.now();
      if (now - lastMoveTime > throttleTime) {
        lastMoveTime = now;
        handleMouseMove(event);
      }
    };

    canvas.addEventListener("mousemove", throttledMouseMove);
    canvas.addEventListener("mousedown", handleClick);
    canvas.addEventListener("contextmenu", (e) => e.preventDefault());

    return () => {
      canvas.removeEventListener("mousemove", throttledMouseMove);
      canvas.removeEventListener("click", handleClick);
      // Reset hover state
      handleHover(null);
    };
  }, [
    gl,
    teethRef,
    isInitialized,
    getViewType,
    handleClick,
    handleHover,
    handleMouseMove,
  ]);

  // Cleanup on unmount
  useEffect(() => {
    mountedRef.current = true;

    return () => {
      mountedRef.current = false;

      // Reset all highlighted materials
      resetAllHighlightedMeshes();

      // Reset hover state on unmount
      setHoveredTooth(null);
    };
  }, [resetAllHighlightedMeshes, setHoveredTooth]);

  return {
    isInitialized,
    resetAllHighlightedMeshes,
  };
};
