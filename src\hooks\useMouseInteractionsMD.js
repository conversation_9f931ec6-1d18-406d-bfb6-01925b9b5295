import { useRef, useState, useEffect, useCallback } from "react";
import * as THREE from "three";
import { createHighlightMaterial } from "../utils/modelUtils";

export const useMouseInteractionsMD = ({
  camera,
  gl,
  patientTeeth,
  setHoveredMDTooth,
  selectedMDTooth,
  setSelectedMDTooth,
  selectedTreatment,
  chartingMDChildPointersRef,
  chartingMDPointersRef,
}) => {
  const raycasterRef = useRef(new THREE.Raycaster());
  const mouseRef = useRef(new THREE.Vector2());
  const prevHoveredRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const lastEventTimeRef = useRef(0);
  const mountedRef = useRef(true);
  const highlightedMeshesRef = useRef(new Set());

  // Reset all highlighted materials when unmounting
  const resetAllHighlightedMeshes = useCallback(() => {
    if (highlightedMeshesRef.current.size > 0) {
      highlightedMeshesRef.current.forEach((mesh) => {
        if (mesh.userData.originalMaterial) {
          mesh.material = mesh.userData.originalMaterial.clone();
        }
      });

      highlightedMeshesRef.current.clear();
    }
  }, []);

  // Get all interactive objects from the teeth
  const getInteractiveObjects = useCallback(() => {
    if (
      (!chartingMDChildPointersRef && !chartingMDPointersRef) ||
      (!chartingMDChildPointersRef.current && !chartingMDPointersRef.current)
    ) {
      return [];
    }

    const objects = [];

    // Skip logging on every mouse move
    const shouldLog = Date.now() - lastEventTimeRef.current > 1000;
    if (shouldLog) {
      lastEventTimeRef.current = Date.now();
    }

    chartingMDChildPointersRef.current.forEach((tooth) => {
      if (tooth) {
        tooth.traverse((child) => {
          if (child.isMesh && child.userData.isInteractive) {
            objects.push(child);
          }
        });
      }
    });
    chartingMDPointersRef.current.forEach((tooth) => {
      if (tooth) {
        tooth.traverse((child) => {
          if (child.isMesh && child.userData.isInteractive) {
            objects.push(child);
          }
        });
      }
    });

    return objects;
  }, [chartingMDChildPointersRef, chartingMDPointersRef]);

  // Handle hover state changes
  const handleHover = useCallback(
    (toothNumber) => {
      if (!mountedRef.current) return;
      if (prevHoveredRef.current === toothNumber) return;

      // Reset previous hover
      if (prevHoveredRef.current) {
        const prevTooth =
          chartingMDPointersRef.current.get(prevHoveredRef.current) ||
          chartingMDChildPointersRef.current.get(prevHoveredRef.current);

        if (prevTooth) {
          prevTooth.traverse((child) => {
            if (
              child.isMesh &&
              child.userData.originalMaterial &&
              highlightedMeshesRef.current.has(child)
            ) {
              try {
                // Create a new instance of the material to avoid sharing
                const restoredMaterial =
                  child.userData.originalMaterial.clone();

                // Ensure all transparency-related properties are properly restored
                if (child.userData.originalMaterial.transparent !== undefined) {
                  restoredMaterial.transparent =
                    child.userData.originalMaterial.transparent;
                }
                if (child.userData.originalMaterial.opacity !== undefined) {
                  restoredMaterial.opacity =
                    child.userData.originalMaterial.opacity;
                }
                if (child.userData.originalMaterial.depthWrite !== undefined) {
                  restoredMaterial.depthWrite =
                    child.userData.originalMaterial.depthWrite;
                }
                if (child.userData.originalMaterial.depthTest !== undefined) {
                  restoredMaterial.depthTest =
                    child.userData.originalMaterial.depthTest;
                }
                if (child.userData.originalMaterial.blending !== undefined) {
                  restoredMaterial.blending =
                    child.userData.originalMaterial.blending;
                }
                if (child.userData.originalMaterial.renderOrder !== undefined) {
                  restoredMaterial.renderOrder =
                    child.userData.originalMaterial.renderOrder;
                }
                if (
                  child.userData.originalMaterial.polygonOffset !== undefined
                ) {
                  restoredMaterial.polygonOffset =
                    child.userData.originalMaterial.polygonOffset;
                }
                if (
                  child.userData.originalMaterial.polygonOffsetFactor !==
                  undefined
                ) {
                  restoredMaterial.polygonOffsetFactor =
                    child.userData.originalMaterial.polygonOffsetFactor;
                }
                if (
                  child.userData.originalMaterial.polygonOffsetUnits !==
                  undefined
                ) {
                  restoredMaterial.polygonOffsetUnits =
                    child.userData.originalMaterial.polygonOffsetUnits;
                }
                if (child.userData.originalMaterial.color !== undefined) {
                  restoredMaterial.color.copy(
                    child.userData.originalMaterial.color,
                  );
                }
                if (child.userData.originalMaterial.emissive !== undefined) {
                  restoredMaterial.emissive.copy(
                    child.userData.originalMaterial.emissive,
                  );
                }

                child.material = restoredMaterial;
                highlightedMeshesRef.current.delete(child);
              } catch (e) {
                
              }
            }
          });
        }
      }

      // Set new hover
      if (toothNumber) {
        const tooth =
          chartingMDPointersRef.current.get(toothNumber) ||
          chartingMDChildPointersRef.current.get(toothNumber);

        if (tooth) {
          tooth.traverse((child) => {
            if (child.isMesh) {
              if (!child.userData.originalMaterial) {
                // Store a clone of the original material
                child.userData.originalMaterial = child.material.clone();
              }
              // Set highlight material - create a new one each time
              child.material = createHighlightMaterial();
              // Add to tracked set
              highlightedMeshesRef.current.add(child);
            }
          });
        }
      }

      prevHoveredRef.current = toothNumber;
      // Always update hovered tooth state regardless of material change
      setHoveredMDTooth(toothNumber);
    },
    [setHoveredMDTooth, chartingMDPointersRef, chartingMDChildPointersRef],
  );

  // Handle mouse move events
  const handleMouseMove = useCallback(
    (event) => {
      if (!mountedRef.current || !isInitialized) return;

      const rect = gl.domElement.getBoundingClientRect();
      mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      raycasterRef.current.setFromCamera(mouseRef.current, camera);

      const interactiveObjects = getInteractiveObjects();

      if (interactiveObjects.length === 0) return;

      const intersects = raycasterRef.current.intersectObjects(
        interactiveObjects,
        true,
      );

      if (intersects.length > 0) {
        const toothNumber = parseInt(intersects[0].object.userData.number);
        if (toothNumber) {
          handleHover(toothNumber);
        }
      } else {
        handleHover(null);
      }
    },
    [camera, getInteractiveObjects, gl, handleHover, isInitialized],
  );

  // Handle click events
  const handleClick = useCallback(
    (event) => {
      if (!mountedRef.current || !isInitialized) return;

      const rect = gl.domElement.getBoundingClientRect();
      mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      raycasterRef.current.setFromCamera(mouseRef.current, camera);

      const interactiveObjects = getInteractiveObjects();
      if (interactiveObjects.length === 0) return;

      const intersects = raycasterRef.current.intersectObjects(
        interactiveObjects,
        true,
      );

      if (intersects.length > 0) {
        const toothNumber = parseInt(intersects[0].object.userData.number);
        if (event.button === 0) {
          // Left click
          // We still want to handle selection for all teeth, even Decay/filling ones
          setSelectedMDTooth((prev) =>
            prev === toothNumber ? null : toothNumber,
          );
          // 
      }
    },
    [getInteractiveObjects, gl, isInitialized, setSelectedMDTooth, camera],
  );

  // Initialize when teeth are available
  useEffect(() => {
    if (
      (chartingMDPointersRef?.current?.size > 0 ||
        chartingMDChildPointersRef?.current?.size > 0) &&
      !isInitialized
    ) {
      setIsInitialized(true);
    }
  }, [chartingMDPointersRef, chartingMDChildPointersRef, isInitialized]);

  // Watch for changes in the teeth ref size with interval polling
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (
        (chartingMDPointersRef?.current?.size > 0 ||
          chartingMDChildPointersRef?.current?.size > 0) &&
        !isInitialized
      ) {
        setIsInitialized(true);
        clearInterval(intervalId);
      }
    }, 500);

    return () => {
      clearInterval(intervalId);
    };
  }, [chartingMDPointersRef, chartingMDChildPointersRef, isInitialized]);

  // Attach event listeners
  useEffect(() => {
    if (!isInitialized || !gl) return;

    // Validate that we have teeth refs before setting up event handlers
    if (
      (!chartingMDPointersRef || !chartingMDPointersRef.current) &&
      (!chartingMDChildPointersRef || !chartingMDChildPointersRef.current)
    ) {
      
      return;
    }

    const canvas = gl.domElement;
    canvas.style.touchAction = "none";

    // Use throttled versions of event handlers for better performance
    let lastMoveTime = 0;
    const throttleTime = 50; // Use original throttle time for better responsiveness

    const throttledMouseMove = (event) => {
      const now = Date.now();
      if (now - lastMoveTime > throttleTime) {
        lastMoveTime = now;
        handleMouseMove(event);
      }
    };

    canvas.addEventListener("mousemove", throttledMouseMove);
    canvas.addEventListener("mousedown", handleClick);
    canvas.addEventListener("contextmenu", (e) => e.preventDefault());

    return () => {
      canvas.removeEventListener("mousemove", throttledMouseMove);
      canvas.removeEventListener("click", handleClick);
      // Reset hover state
      handleHover(null);
    };
  }, [
    gl,
    chartingMDPointersRef,
    chartingMDChildPointersRef,
    isInitialized,
    handleClick,
    handleHover,
    handleMouseMove,
  ]);

  // Cleanup on unmount
  useEffect(() => {
    mountedRef.current = true;

    return () => {
      mountedRef.current = false;

      // Reset all highlighted materials
      resetAllHighlightedMeshes();

      // Reset hover state on unmount
      setHoveredMDTooth(null);
    };
  }, [resetAllHighlightedMeshes, setHoveredMDTooth]);

  return {
    isInitialized,
    resetAllHighlightedMeshes,
  };
};
